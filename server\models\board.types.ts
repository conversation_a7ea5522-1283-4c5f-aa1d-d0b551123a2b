export type action = "disconnect" | "connect" ;

export interface item {
  documentId: string;
}

export interface connectionAction {
  source: item;
  target: item;
  action: action;
}

export interface IBoardRequest {
  teamId: string;
  items: connectionAction[];
}

export interface IBoardFunctionResponse {
  documentId: string;
}

export interface IBoardResponse {
  status?: string;
  documentId?: string;
  error?: string;
  action: action;
}