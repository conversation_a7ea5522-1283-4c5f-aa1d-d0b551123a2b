import axios from "axios";
import qs from "qs";
import type { connectionAction } from "../models/board.types";

interface IStrapiResponse {
  data: {
    data: any[];
    meta: {
      pagination: {
        total: number;
      };
    };
  };
}

function formatComponentString(dynamicZone: any, componentPrefix: string): any {
  const newDynamicZone = dynamicZone.map((c: any) => {
    c.__component = c.__component.replace(`${componentPrefix}.`, "");
    return c;
  });

  return newDynamicZone;
}

async function strapiRequest(
  endpoint: string,
  query: object,
): Promise<IStrapiResponse | { error: string }> {
  const qsSlug = qs.stringify(query, {
    encodeValuesOnly: true,
  });
  const r: IStrapiResponse = await axios.get(`${process.env.CMS_URL}/api/${endpoint}?${qsSlug}`, {
    headers: {
      Authorization: `Bearer ${process.env.CMS_KEY}`,
    },
  });

  if (r.data.meta.pagination.total === 0) {
    return {
      error: "Not Found",
    };
  }

  return r;
}

interface IFlag {
  name: string;
  flag?: string;
  points: number;
  phase: string;
}

async function getFlags(): Promise<IFlag[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-flags", {
    populate: {
      tda_core_phase: {
        fields: ["state"],
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  const flags = r.data.data.map((d) => ({
    name: d.name,
    points: d.points,
    phase: d.tda_core_phase.state,
  }));

  return flags;
}

interface ITeamItems {
  tda_ctf_item: {
    boardPosition: number;
    components: any[];
  };
}

async function getTeamItems(teamId: string): Promise<ITeamItems[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-team-items", {
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_item: {
        fields: ["*"],
        populate: ["components"],
      },
    },
    filters: {
      tda_core_team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  // Transform data by extracting only needed properties
  const cleanedData = r.data.data.map((d) => ({
    tda_ctf_item: {
      boardPosition: d.tda_ctf_item.boardPosition,
      components: formatComponentString(d.tda_ctf_item.components, "td-a-ctf-platform-items"),
    },
  }));

  return cleanedData;
}

async function getTeamFlags(teamId: string): Promise<IFlag[] | { error: string }> {
  const r = await strapiRequest("tda-ctf-team-flags", {
    populate: {
      tda_core_team: {
        fields: ["name"],
      },
      tda_ctf_flag: {
        fields: ["*"],
        populate: ["tda_core_phase"],
      },
    },
    filters: {
      tda_core_team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  const cleanedData = r.data.data.map((d) => ({
    name: d.tda_ctf_flag.name,
    flag: d.tda_ctf_flag.flag,
    points: d.tda_ctf_flag.points,
    phase: d.tda_ctf_flag.tda_core_phase.state,
  }));

  return cleanedData;
}

async function getConnections(
  teamId: string,
  item: connectionAction,
): Promise<any | { error: string }> {
  const r = await strapiRequest("tda-ctf-connections", {
    populate: {
      team: {
        fields: ["name"],
      },
      items: {
        fields: ["*"],
      },
    },
    filters: {
      $and: [
        {
          items: {
            documentId: {
              $eq: item.source.documentId,
            },
          },
        },
        {
          items: {
            documentId: {
              $eq: item.target.documentId,
            },
          },
        },
      ],
    },
    team: {
      documentId: {
        $eq: teamId,
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  return r.data.data[0];
}

async function removeConnection(connectionId: string) {
  const r = await axios.delete(`${process.env.CMS_URL}/api/tda-ctf-connections/${connectionId}`, {
    headers: {
      Authorization: `Bearer ${process.env.CMS_KEY}`,
    },
  });
  if (r.status === 200) {
    console.log("Connection removed");
    return r.data;
  }
  return r;
}

async function createConnection(teamId: string, items: connectionAction) {
  const r = await axios.post(
    `${process.env.CMS_URL}/api/tda-ctf-connections`,
    {
      data: {
        team: teamId,
        items: [items.source.documentId, items.target.documentId],
      },
    },
    {
      headers: {
        Authorization: `Bearer ${process.env.CMS_KEY}`,
      },
    },
  );

  if (r.status > 399) {
    console.log("Error creating connection");
    return r;
  }

  const bR = await strapiRequest("tda-ctf-boards", {
    populate: {
      team: {
        fields: ["name"],
      },
      connections: {
        fields: ["*"],
      },
    },
    filters: {
      team: {
        documentId: {
          $eq: teamId,
        },
      },
    },
  });

  if ("error" in bR) {
    console.log("Error getting board");
    return r;
  }

  bR.data.data[0].connections.push(r.data.data.documentId);
  console.log(bR.data.data[0].connections);
  let oldConnections: any[] = bR.data.data[0].connections.map((c: any) => ({
    documentId: c.documentId,
  }));
  oldConnections = oldConnections.filter((c: any) => c.documentId !== "");
  oldConnections = oldConnections.filter((c: any) => c.documentId !== null);
  oldConnections = oldConnections.filter((c: any) => c.documentId !== undefined);
  oldConnections.push({ documentId: r.data.data.documentId });
  console.log(
    JSON.stringify({
      team: teamId,
      connections: {
        connect: oldConnections,
      },
    }),
  );

  await axios.put(
    `${process.env.CMS_URL}/api/tda-ctf-boards/${bR.data.data[0].documentId}`,
    {
      data: {
        team: teamId,
        connections: {
          connect: oldConnections,
        },
      },
    },
    {
      headers: {
        Authorization: `Bearer ${process.env.CMS_KEY}`,
      },
    },
  );

  return r;
}

async function getTeamConnections(
  teamId: string,
  itemId: string,
): Promise<any | { error: string }> {
  const r = await strapiRequest("tda-ctf-connections", {
    populate: {
      team: {
        fields: ["name"],
      },
      items: {
        fields: ["*"],
      },
    },
    filters: {
      items: {
        documentId: {
          $eq: itemId,
        },
      },
    },
    team: {
      documentId: {
        $eq: teamId,
      },
    },
  });

  if ("error" in r) {
    return r as { error: string };
  }

  return r.data.data;
}

export {
  getFlags,
  getTeamItems,
  getTeamFlags,
  getConnections,
  removeConnection,
  createConnection,
  getTeamConnections,
};
