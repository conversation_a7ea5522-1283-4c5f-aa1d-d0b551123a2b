import { getTeamConnections } from "./strapi";

async function checkConnections(teamId: string, itemId: string) {
  const visitedItems = new Set<string>(); // Track visited items to avoid cycles
  const connectedItems: string[] = []; // Store all connected item IDs
  const queue: string[] = [itemId]; // Queue for BFS traversal

  // Add the starting item to visited set
  visitedItems.add(itemId);

  while (queue.length > 0) {
    const currentItemId = queue.shift()!; // Get next item to process

    // Get all connections for the current item
    const connections = await getTeamConnections(teamId, currentItemId);
    if (!connections || connections.error) {
      continue; // Skip if no connections found
    }

    // Process each connection
    for (const connection of connections) {
      if (!connection.items || !Array.isArray(connection.items)) {
        continue; // Skip if items array is missing
      }

      // Find the other item(s) in this connection
      for (const item of connection.items) {
        const itemDocumentId = item.documentId;

        // Skip if we've already visited this item
        if (visitedItems.has(itemDocumentId)) {
          continue;
        }

        // Add to visited set and queue for further exploration
        visitedItems.add(itemDocumentId);
        queue.push(itemDocumentId);
        connectedItems.push(itemDocumentId);
      }
    }
  }

  return connectedItems;
}

export { checkConnections };
