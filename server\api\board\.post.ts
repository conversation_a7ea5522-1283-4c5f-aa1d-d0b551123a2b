import type { connectionAction, IBoardRequest } from "~~/server/models/board";
import { createConnection, getConnections, removeConnection } from "~~/server/utils/strapi";

async function connectItems(teamId: string, items: connectionAction) {
  const sourceConnection = await getConnections(teamId, items);

  if (!sourceConnection || !targetConnection) {
    console.log("No connections found at first");
    const r = await createConnection(teamId, items);
    console.log("created connection");
    return r.data.data;
  }

  const isConnected = sourceConnection.items.map((v: any) => {
    console.log(v.documentId, items.target.documentId, items.source.documentId);

    return v.documentId === items.target.documentId || v.documentId === items.source.documentId;
  });

  console.log("isConnected", isConnected);

  if (isConnected[0] && isConnected[1]) {
    await removeConnection(sourceConnection[0].documentId);
    console.log("removed connection");
    return { status: "removed" };
  }

  const r = await createConnection(teamId, items);
  console.log("created connection");
  return r.data.data;
}

export default defineEventHandler(
  async (event): Promise<any | { status: string } | { error: string }> => {
    const body: IBoardRequest = await readBody<IBoardRequest>(event);
    if (!body.teamId) {
      // TODO: After keycloak implementation get teamId from keycloak and not via post req
      return {
        error: "No team ID provided",
      };
    }

    if (!body.items) {
      return {
        error: "No items provided",
      };
    }

    for (const item of body.items) {
      switch (item.action) {
        case "connect":
          return await connectItems(body.teamId, item);
        //   case "disconnect":
        //     await disconnectItems(item);
        //     break;
        //   case "update":
        //     await updateItems(item);
        //     break;
      }
    }

    return body;
  },
);
