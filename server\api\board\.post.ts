import type {
  connectionAction,
  IBoardFunctionResponse,
  IBoardRequest,
  IBoardResponse,
} from "~~/server/models/board.types";
import { checkConnections } from "~~/server/utils/connections";
import { createConnection, getConnections, removeConnection } from "~~/server/utils/strapi";

async function disconnectItems(
  teamId: string,
  items: connectionAction,
): Promise<IBoardFunctionResponse | { status: string }> {
  const connection = await getConnections(teamId, items);
  if (!connection || connection.error) {
    return { status: "no connection" };
  }
  await removeConnection(connection.documentId);
  console.log("removed connection");
  return { status: "removed" };
}

async function connectItems(
  teamId: string,
  items: connectionAction,
): Promise<IBoardFunctionResponse | { status: string }> {
  const connection = await getConnections(teamId, items);

  if (!connection || connection.error) {
    console.log("No connections found");
    const r = await createConnection(teamId, items);
    console.log("created connection");

    // console.log(await checkConnections(teamId, items.source.documentId));

    return {
      documentId: r.data.data.documentId,
    };
  }

  return await disconnectItems(teamId, items);
}

export default defineEventHandler(
  async (event): Promise<IBoardResponse[] | { status: string } | { error: string }> => {
    const body: IBoardRequest = await readBody<IBoardRequest>(event);
    const actions: IBoardResponse[] = [];
    if (!body.teamId) {
      // TODO: After keycloak implementation get teamId from keycloak and not via post req
      return {
        error: "No team ID provided",
      };
    }

    if (!body.items) {
      return {
        error: "No items provided",
      };
    }

    for (const item of body.items) {
      switch (item.action) {
        case "connect":
          actions.push({ ...(await connectItems(body.teamId, item)), action: item.action });
          break;
        case "disconnect":
          actions.push({ ...(await disconnectItems(body.teamId, item)), action: item.action });
          break;
        default:
          actions.push({ error: "Invalid action", action: item.action });
          break;
      }
    }

    return actions;
  },
);
